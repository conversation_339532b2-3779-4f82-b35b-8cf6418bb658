import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.eddars.rockpaperscissors"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions { jvmTarget = "11" }

    defaultConfig {
        applicationId = "com.eddars.rockpaperscissors"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        // Optimisations spécifiques pour Samsung Galaxy Ultra
        ndk {
            abiFilters += listOf("arm64-v8a") // Priorité ARM64 pour Galaxy Ultra
        }

        // Configuration pour les appareils haute performance
        manifestPlaceholders["supportsRtl"] = "true"
        manifestPlaceholders["largeHeap"] = "true"
    }

    // Charger les propriétés du keystore
    val keystoreProperties = Properties()
    val keystorePropertiesFile = rootProject.file("key.properties")
    if (keystorePropertiesFile.exists()) {
        keystoreProperties.load(FileInputStream(keystorePropertiesFile))
    }

    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties.getProperty("keyAlias")
            keyPassword = keystoreProperties.getProperty("keyPassword")
            storeFile = keystoreProperties.getProperty("storeFile")?.let { file(it) }
            storePassword = keystoreProperties.getProperty("storePassword")
        }
    }

    buildTypes {
        getByName("debug") {
            ndk {
                abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64", "x86")
            }
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
            packagingOptions {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        getByName("release") {
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a", "armeabi-v7a")
            }
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

            // Optimisations pour Galaxy Ultra
            packagingOptions {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
    }

    packaging {
        jniLibs {
            useLegacyPackaging = true
            pickFirsts += listOf(
                "**/libmediapipe_tasks_vision_jni.so",
                "**/libmediapipe_tasks_core_jni.so",
                "**/libmediapipe_jni.so"
            )
            excludes += listOf("**/libmediapipe_tasks_vision_jni_unsupported.so")
        }
        resources {
            pickFirsts += listOf(
                "META-INF/LICENSE.md",
                "META-INF/LICENSE-notice.md",
                "META-INF/DEPENDENCIES"
            )
        }
    }
}

flutter { source = "../.." }

dependencies {
    implementation("androidx.camera:camera-core:1.3.4")
    implementation("androidx.camera:camera-camera2:1.3.4")
    implementation("androidx.camera:camera-lifecycle:1.3.4")
    implementation("androidx.camera:camera-view:1.3.4")

    implementation("com.google.mediapipe:tasks-vision:0.10.5") {
        exclude(group = "com.google.protobuf", module = "protobuf-java")
    }
    implementation("com.google.mediapipe:tasks-core:0.10.5") {
        exclude(group = "com.google.protobuf", module = "protobuf-java")
    }

    implementation("com.google.protobuf:protobuf-javalite:3.19.4")
}
