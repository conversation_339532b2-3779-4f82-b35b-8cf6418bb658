package com.eddars.rockpaperscissors.gesture

import android.os.Build
import android.util.Log
import android.content.Context
import android.app.ActivityManager

/**
 * Optimiseur spécifique pour les appareils Samsung Galaxy Ultra
 * Gère les problèmes de compatibilité et de performance
 */
object SamsungGalaxyOptimizer {
    
    private const val TAG = "SamsungGalaxyOptimizer"
    
    // Modèles Samsung Galaxy Ultra connus
    private val GALAXY_ULTRA_MODELS = setOf(
        "SM-S918", // Galaxy S23 Ultra
        "SM-S928", // Galaxy S24 Ultra
        "SM-S938", // Galaxy S24 Ultra (variantes)
        "SM-S926", // Galaxy S24+
        "SM-S916", // Galaxy S23+
        "SM-S921", // Galaxy S24
        "SM-S911"  // Galaxy S23
    )
    
    /**
     * Vérifie si l'appareil est un Samsung Galaxy Ultra
     */
    fun isGalaxyUltraDevice(): Boolean {
        val model = Build.MODEL
        val manufacturer = Build.MANUFACTURER
        
        Log.d(TAG, "🔍 Device check - Manufacturer: $manufacturer, Model: $model")
        
        if (manufacturer.equals("samsung", ignoreCase = true)) {
            val isUltra = GALAXY_ULTRA_MODELS.any { model.startsWith(it, ignoreCase = true) }
            Log.d(TAG, "📱 Samsung device detected, Ultra model: $isUltra")
            return isUltra
        }
        
        return false
    }
    
    /**
     * Obtient la configuration optimale pour MediaPipe sur Galaxy Ultra
     */
    fun getOptimalMediaPipeConfig(): MediaPipeConfig {
        val isUltra = isGalaxyUltraDevice()
        
        return if (isUltra) {
            Log.d(TAG, "⚡ Applying Galaxy Ultra optimizations")
            MediaPipeConfig(
                useGPUDelegate = false, // Utiliser CPU pour éviter les problèmes GPU
                targetResolutionWidth = 224, // Résolution plus basse pour Galaxy Ultra
                targetResolutionHeight = 224,
                minConfidenceScore = 0.7, // Score plus élevé pour plus de précision
                maxNumHands = 1, // Limiter à une main pour les performances
                enableFaceDetection = false,
                useOptimizedModel = true
            )
        } else {
            Log.d(TAG, "📱 Using standard configuration for non-Ultra device")
            MediaPipeConfig(
                useGPUDelegate = true,
                targetResolutionWidth = 256,
                targetResolutionHeight = 256,
                minConfidenceScore = 0.65,
                maxNumHands = 2,
                enableFaceDetection = true,
                useOptimizedModel = false
            )
        }
    }
    
    /**
     * Vérifie la mémoire disponible et recommande des optimisations
     */
    fun checkMemoryAndOptimize(context: Context): MemoryOptimization {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val availableMemoryMB = memoryInfo.availMem / (1024 * 1024)
        val totalMemoryMB = memoryInfo.totalMem / (1024 * 1024)
        val memoryUsagePercent = ((totalMemoryMB - availableMemoryMB) * 100) / totalMemoryMB
        
        Log.d(TAG, "💾 Memory status - Available: ${availableMemoryMB}MB, Total: ${totalMemoryMB}MB, Usage: ${memoryUsagePercent}%")
        
        return when {
            memoryUsagePercent > 85 -> {
                Log.w(TAG, "⚠️ High memory usage detected, applying aggressive optimizations")
                MemoryOptimization.AGGRESSIVE
            }
            memoryUsagePercent > 70 -> {
                Log.w(TAG, "⚠️ Moderate memory usage, applying standard optimizations")
                MemoryOptimization.MODERATE
            }
            else -> {
                Log.d(TAG, "✅ Memory usage is normal")
                MemoryOptimization.NONE
            }
        }
    }
    
    /**
     * Applique les optimisations spécifiques selon le niveau de mémoire
     */
    fun applyMemoryOptimizations(optimization: MemoryOptimization): CameraConfig {
        return when (optimization) {
            MemoryOptimization.AGGRESSIVE -> {
                Log.d(TAG, "🔥 Applying aggressive optimizations")
                CameraConfig(
                    targetResolution = 160 to 160,
                    frameRate = 15,
                    enableImageStabilization = false,
                    bufferSize = 1
                )
            }
            MemoryOptimization.MODERATE -> {
                Log.d(TAG, "⚡ Applying moderate optimizations")
                CameraConfig(
                    targetResolution = 224 to 224,
                    frameRate = 20,
                    enableImageStabilization = false,
                    bufferSize = 2
                )
            }
            MemoryOptimization.NONE -> {
                Log.d(TAG, "📱 Using standard configuration")
                CameraConfig(
                    targetResolution = 256 to 256,
                    frameRate = 30,
                    enableImageStabilization = true,
                    bufferSize = 3
                )
            }
        }
    }
    
    /**
     * Détecte les problèmes spécifiques aux Galaxy Ultra
     */
    fun detectGalaxyUltraIssues(context: Context): List<String> {
        val issues = mutableListOf<String>()
        
        if (isGalaxyUltraDevice()) {
            // Vérifier la version Android
            if (Build.VERSION.SDK_INT >= 34) { // Android 14+
                issues.add("Android 14+ detected - may need additional GPU memory management")
            }
            
            // Vérifier la mémoire
            val memoryOpt = checkMemoryAndOptimize(context)
            if (memoryOpt != MemoryOptimization.NONE) {
                issues.add("High memory usage detected - applying optimizations")
            }
            
            // Vérifier le processeur
            val cpuAbi = Build.SUPPORTED_ABIS[0]
            if (!cpuAbi.contains("arm64")) {
                issues.add("Non-ARM64 architecture detected - may have compatibility issues")
            }
        }
        
        return issues
    }
}

/**
 * Configuration MediaPipe optimisée
 */
data class MediaPipeConfig(
    val useGPUDelegate: Boolean,
    val targetResolutionWidth: Int,
    val targetResolutionHeight: Int,
    val minConfidenceScore: Double,
    val maxNumHands: Int,
    val enableFaceDetection: Boolean,
    val useOptimizedModel: Boolean
)

/**
 * Configuration caméra optimisée
 */
data class CameraConfig(
    val targetResolution: Pair<Int, Int>,
    val frameRate: Int,
    val enableImageStabilization: Boolean,
    val bufferSize: Int
)

/**
 * Niveaux d'optimisation mémoire
 */
enum class MemoryOptimization {
    NONE,
    MODERATE,
    AGGRESSIVE
}
